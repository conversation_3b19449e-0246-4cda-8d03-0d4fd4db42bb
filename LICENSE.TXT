This is an xdvdfs (xbox iso) file creation/extraction utility for linux/darwin/
freebsd.

If you want to port it to your OS it shouldn't be too hard, just make sure your
fseek/ftell takes 64-bit file offsets.

I wrote this code completely from scratch today because xbiso (the one on
sourceforge) keeps seg-faulting and the code is buggy.  I got useful
documentation on the xdvdfs file system from http://xbox-linux.sourceforge.net.

I will say thanks though to the author of xbiso, it was a good start and got me
thinking!


Regarding licensing:

I think the GPL sucks!  (it stands for Generosity Poor License)

My open-source code is really *FREE* so you can do whatever you want with it, as
long as 1) you don't claim that you wrote my code and 2) you retain a notice
that some parts of the code <NAME_EMAIL> and 3) you understand
there are no warranties.  I only guarantee that it will take up disk space!

If you want to help out with this project it would be welcome, just email me at
<EMAIL>.

This code <NAME_EMAIL> and is licensed under a slightly
modifified version of the Berkeley Software License, which follows:

/*
 * Copyright (c) 2003 in <<EMAIL>>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *
 *    This product includes software developed by in <<EMAIL>>.
 *
 * 4. Neither the name of "in" nor the email address "<EMAIL>"
 *    may be used to endorse or promote products derived from this software
 *    without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED `AS IS' AND ANY EXPRESS OR IMPLIED WARRANTIES
 * INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
 * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE
 * AUTHOR OR ANY CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF
 * ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

Please send bug reports to <NAME_EMAIL>

To compile, run 'make' in the source directory.

NOTE:  You need to be running GNU make and /bin/bash must be executable

Enjoy!

in version 1.0
March 10, 2003
