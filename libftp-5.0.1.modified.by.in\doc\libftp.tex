\documentstyle[cxx,fancyheadings,twoside,epsf,indentfirst]{article}
% Vertical sizes
%\vsize=20cm
%\voffset=-2.3cm
%\topmargin=0cm
%\headheight=0.9cm
%\footskip=1cm
%\footheight=0.9cm
%\textheight=16cm
%\headrulewidth 0.01cm
%\footrulewidth 0.0cm
% 0 sizes
%\hsize=30cm
%\hoffset=-4.3cm
%\hoffset=-2.3cm	
%\textwidth=13cm
% Modes
% \special{landscape}
\pagestyle{empty}
\pagestyle{fancyplain}
\newcommand{\tit}[1]{#1}
\rhead[\fancyplain{}{\tit{\leftmark}}]{\fancyplain{}{\tit{\rightmark}}}
\lhead[\fancyplain{}{\tit{\rightmark}}]{\fancyplain{}{\tit{\leftmark}}}
\chead{\hfill}
\lfoot[\fancyplain{}{\tit{\thepage}}]{\fancyplain{}{\hfill}}
\rfoot[\fancyplain{}{\hfill}]{\fancyplain{}{\tit{\thepage}}}
\cfoot{\hfill}
\renewcommand{\sectionmark}[1]{\markboth{#1}{\ }}
\renewcommand{\subsectionmark}[1]{\markright{\ }}
\newcommand{\look}[1]{(Chapter~\ref{#1}, page~\pageref{#1})}
\newcommand{\toindex}[1]{\underline{\bf#1}\index{#1}}
\newcommand{\add}[1]{\symbol{64}}
\newcommand{\ps}[1]{\symbol{37}s}
\newcommand{\twcol}[4]{
\noindent\parbox[t]{#1\textwidth}{#3} \hfill \parbox[t]{#2\textwidth}{#4\hfill}\\
}
\newcommand{\tc}[2]{\twcol{0.49}{0.49}{#1}{#2}}
\newcommand{\tcc}[2]{\twcol{0.49}{0.49}{\toindex{#1}}{#2}}
\newcommand{\ttt}[2]{\bigskip

{\bf#1}

#2}
\newcommand{\ts}[1]{{\underline{\bf#1}}}
\newcommand{\dl}[2]{\parbox[t]{0.4\textwidth}{#1\hfill}\hfill
                    \parbox[t]{0.4\textwidth}{#2\hfill}}
\makeindex
\begin{document} 
\title{{\bf\it{}LIBFTP User's guide}\\Version 5.0}
\author{Oleg Orel\\\\\\<EMAIL>\\<EMAIL>}
\date{December 13,1995}
\newpage
\maketitle

\section*{License}

This library is designed for free, non-commercial software creation. 
It is changeable and can be improved. The author would greatly appreciate 
any advises, new components and patches of the existing programs.
Commercial usage is also possible with participation of it's author
\footnote{For use this library for commercial purpose need to buy license, for do it
need to contact with author}

\section*{Introduction}

The basic orientation of this library is making user's programs which transport
files via TCP/IP network. It contains set of functions, 
starting from primitive, such as opening FTP connection to the server, 
and finishing by high-level functions, such as functions which retrieve files
 via network, making and closing channels to the server. All functions have 
prototypes in common header file named \toindex{FtpLibrary.h}, 
which must be 
available in standard headers directory
\footnote{for example ``/usr/include''}.
Those prototypes almost fully 
describe orientation and arguments of all functions, 
but common ideology and library components should be mentioned. 

This library is a client and uses standard FTPD from the other side.

There are problems of errors processing in many operating systems including input/output errors. 
The mutual mechanism of value returning of all functions is used in this library.
(EXIT macros, defined in file FtpLibrary.h). This mechanism allows,
 after the definition of the error processing functions, write programs, 
considering the conditions to be ideal.
Data transfer functions have possibility to preset data stream 
expectation timeout.
When the set time expires, previously set function will be called.

\section*{Last major changes}

Last time to library was added several extensions for high level operations: such as 
FtpStat \look{FtpStat} which extract maximum of information about specie-fed file(s) 
and FtpHTTPGet\look{FtpHttpGet} which receive files using as gateway HTTP-server. 

\section{Variables and definitions} 

\subsection{Some definitions in libftp's header file (FtpLibrary.h)}

\ttt{\toindex{EXIT}}{Main macro for return value from library's functions with 
calling handlers if it's need}

\ttt{\toindex{MAX\_ANSWERS}}{Number of possible good answers from FTPD for one request}

\ttt{\toindex{FTP\_NFDS}}{Maximum numbers of one-time opened files in system}

\ttt{\toindex{FTPBUFSIZE}}{Size of block for transmit data via network. 
By default equivalence \toindex{BUFSIZ}}

\ttt{\toindex{LQUIT}}{Error status of local functions. If you give this status 
from libftp's function you must use perror for expand diagnostic.}

\ttt{\toindex{QUIT}}{Error status of network operation. Use perror.}

\ttt{\toindex{Ctrl}(char)}{Return control character code}

\ttt{\toindex{FtpString}}{Special type for strings, entering for readable} 

\ttt{\toindex{STATUS}}{Special type for returned value, if function return value such type, 
it is mean that you must check returned value and compare it with OK, QUIT, LQUIT 
if handlers wasn't preseted} 

\ttt{\toindex{FREE}(data)}{Fill data by zero}

\ttt{\toindex{FtpError}(libftp's call)}{Special macro for diagnostic bad conditions}

\ttt{\toindex{FtpAssert}(libftp's call)}{Special macro for automatically return from
this function if status is bad}

\ttt{\toindex{FtpStatus}(FTP *,libftp's call)}{This macro executes specified function 
and return his status, without interruption to handlers inside if last one was set}

\subsection{Libftp's file specification}

All files which must be interprets as local interprets as libftp's files. 
LIBFTP responds to three types of files such
 as local file, FTP's files and program
pipes. All files can be described as next syntax:

\ttt{$\mid$string}{interprets string as shell command, which must be 
   executed with appropriate input/output for file. It depends where
   this file is specified.}

\ttt{*STDIN*, *STDOUT*, *STDERR* or char '-'}{opened standard streams}

\ttt{anything else}{local file}




\subsection{The FTP data structure} \label{FTP}

\subsubsection{The members of FTP structure}

\tc{FILE *\toindex{sock}\footnote{You can use macro FTPCMD(ftp) for extract
this members, using this macro for making your program more compatibility 
with next versions of this library}}
{--- command channel to the server;}

\tc{FILE *\toindex{data}\footnote{You can use macro FTPDATA(ftp) for extract
this members, using this macro for making your program more compatibility 
with next versions of this library}}
{--- pointer to data structure, which describes data channel to the server;}

\tc{int \toindex{errno}}{ --- last returned value. When value is lower than 1, an error occurred;}

\tc{char \toindex{mode}}{--- type of transfer (valid values: 'A' 'I' ....);}

\tc{int \toindex{ch}}{--- help variable. Is used to convert ASCII files, 
user of library for cleaning your problems must forget about this member;}

\tc{STATUS (*\toindex{error})()}{---  pointer to an error handler. It is called 
                       when status from the server is bad;}
\tc{STATUS (*\toindex{debug})()}{--- pointer to a debug handler. Is called from  
                        functions of sending/receiving messages to/from server;}

\tc{STATUS (*\toindex{IO})()}{--- pointer to Input/Output error handler. Is called when 
channel to server is broken.}

\tc{STATUS (*\toindex{hash})()}{--- pointer to function, which must compute 
summary traffic. This function can take one argument which describe
 how many bytes 
now received of sended to/from server. If the argument is equivalence 
to zero, then counter must be reset to zero. But of course user can use 
this handler for another properties of herself program, for example for 
perriodicaly called anything else for checking other conditions, because 
the transfer procedure can take large time from user's program.}

\tc{int \toindex{seek}}{--- the first byte in file for transfer. This option
can use for re-transfer file again after connection is broken}

\tc{int \toindex{flags}}{--- the option list for transfer procedures such as:
\\
\begin{itemize}
\item[FTP\_REST] Turn on re-transfer file using method of compare size of files
in both sides. 
\item[FTP\_NOEXIT] Don't exit from standard error and IO handlers 
\item[FTP\_HANDLERS] Enable to call handlers for errors and debug events. 
                     Set by default. 
\end{itemize}}

\tc{struct timeval \toindex{timeout}}{--- Timeout for send/receive procedures}

\tc{int \toindex{port}}{--- Port for making command connection}

\tc{FtpString \toindex{title}}{--- Connection identification}

\tc{unsigned long \toindex{counter}}{--- counter of already transferred bytes}

\subsubsection{Initialization of FTP structure}

This library have two special objects: procedure FtpCreateObject and external
static structure FtpInit. The procedure FtpCreateObject called from 
FtpConnect. The structure FtpInit can be modified by hand or by using special
macros such as \toindex{FtpSetFlag}, \toindex{FtpClearFlag}, \toindex{FtpTestFlag}, \toindex{FtpSetPort}, \toindex{FtpSetTimeout}, \toindex{FtpSetErrorHandler}, \toindex{FtpSetDebugHandler}, \toindex{FtpSetIOHandler}, \\ 
\toindex{FtpSetHashHandler},\toindex{FtplibDebug}.

\subsection{The \toindex{ARCHIE}  data structure}

The \ts{ARCHIE} data structure using only with function FtpArchie for extract
result of works one. This structure have four members such as:

\tc{struct tm \toindex{createtime}}{Time of file creation.}

\tc{unsigned long \toindex{size}}{size of file.}

\tc{FtpString \toindex{host}}{Host which file is located}

\tc{FtpString \toindex{file}}{Full path in pointed host of this file}


\subsection{The \toindex{FTP\_STAT}  data structure}\label{FtpStat}

The \ts{FTP\_STAT} data structure using with function \toindex{FtpStat} and \toindex{FtpStatFree} for get file's properties in remote server. Using this functions and this structure you can get size of files, protection, owners and types for file.

\tc{FtpString \toindex{name}}{Name of file which described in current structure}

\tc{char \toindex{type}}{Type of file: '-' - regular file, 'd' - directory, 'l' - symbolic link}

\tc{int \toindex{mode}}{Protection of file.}

\tc{int \toindex{inodes}}{Number of references to this file}

\tc{FtpString \toindex{user}}{Owner's name of this file}

\tc{FtpString \toindex{group}}{Group's name of this file}

\tc{unsigned long \toindex{size}}{Size of file in bytes}

\tc{int \toindex{month}}{Month of creation file}

\tc{int \toindex{day}}{Day of creation file}

\tc{FtpString \toindex{time}}{Time of creation file in string format}

\tc{FtpString \toindex{link}}{If file is symbolic link, here is name of pointed file}

\tc{FTP\_STAT *\toindex{next}}{Pointer to next same structure, otherwise NULL }



\section{Library's routines}

\subsection{Connection/Disconnection with server}

\ttt{STATUS \toindex{FtpConnect}(FTP~**, char~*hostname
\footnote{The name of the host may be symbolic (for example \ts{dxcern.cern.ch}) or numeric (for example \ts{**************})}
)}
{
  Makes channel to the server, at the ``hostname'' machine.
  Creates FTP data structure and returns pointer to it. If the procedure \toindex{FtplibDebug}(1)
was previously called, \ts{FtpConnect} calls automatically \ts{FtpDebug} for the \ts{debug mode} to be turned on. 
  \look{debug}.
}
\ttt{STATUS \toindex{FtpUser}(FTP~*, char~*user)}
{
 Sends the name of the user to the server. The connection must be done before it.
}

\ttt{STATUS \toindex{FtpPassword}(FTP~*, char~*password)}
{
 Sends \ts{password} to the server. The function \ts{FtpUser} must be called before it.
}

\ttt{STATUS \toindex{FtpAccount}(FTP~*, char~*account)}
{
 Sends a name of the account to the server. The name of the account is not standard
 attribute for many systems, so this function is used very seldom.
 The function \ts{FtpPassword} must be called before it.
}

\ttt{
STATUS \toindex{FtpLogin}(FTP~**, char~*hostname, char~*user, char~*password, char~*account)}
{
  Executes functions \ts{FtpConnect}, \ts{FtpUser}, \ts{FtpPassword},
  \ts{FtpAccount} (if necessary)  consistently. If the name of the account is absent, 
  replaces it with the \ts{NULL} value.
}

\ttt{STATUS \toindex{FtpBye}(FTP~*)}
{ Finishes work with the server and closes all channels. 
\footnote{You can see from the description of connect/disconnect functions, that you can create 
more than one connection to servers simultaneously.}
}

\ttt{STATUS \toindex{FtpQuickBye}(FTP~*)}
{ Fast close data and command connection to server without delays for waiting
server's confirmation and destroying the FTP object.
}

\ttt{STATUS \toindex{FtpAbort}(FTP~*)}
{ Abort last command passed to server}


\subsection{The debugging} \label{debug} 

There is a possibility to predefine few functions, 
such as:~\footnote{If the \ts{NULL} value is transferred as a parameter \ts{``function''} to the functions, described below,
the handling will be turned off.}

\ttt{\toindex{FtpSetDebugHandler}(FTP *,function)}
{  Predefines function of protocol debugging.
   After the function is predefined, it is called with every 
   sending/receiving messages from the server.
   The function, defined as a debug handler must do returns to the calling 
functions (\ts{FtpSendMessage}/\ts{FtpGetMessage}), but can also abort the program. 

}

\ttt{\toindex{FtpSetErrorHandler}(FTP *,function)}
{
   Predefines error handler. If the server's answer means, that the operation is not finished 
 correctly, this function will be called.
   The result code is negative, if an error is occurs.
}
\ttt{\toindex{FtpSetIOHandler}(FTP *,function)}
{
   Predefines handler of Input/Output processing. This function is called, when a connection to the  
   server is broken. For example, when the network or the remote host is down. This handler also is
   called after the  \toindex{timeout} of one character waiting expires.   
}

\ttt{\toindex{FtpDebug}(FTP *)}
{
Turns on all standard debugging functions. 

\tc{\toindex{FtpDebugError}}{--- prints a string, taken from the server, and aborts the program;}
\tc{\toindex{FtpDebugDebug}}{--- prints a string, taken from the server;}
\tc{\toindex{FtpDebugIO}}{--- prints string \ts{strerror(errno)} and aborts the program.}
}


All function for debugging have three arguments:\\
1. Pointer to FTP data structure;\\
2. Last returned value from the server. When errors occur, the value is less than 1;\\
3. Diagnostic string.(char *)

\ttt{\toindex{FtplibDebug}(yes|no)}
{	Turns on/off autostart debug mode, when connection is established.
}

\ttt{\toindex{FtpSetHashHandler}(FTP *,function)}
{
   Predefines handler of function which must compute traffic size. This 
function have only one argument which describe number of transferred bytes.
If this argument is zero counter must be reset to zero.
}


\ttt{\toindex{FtpLog}(char *name\_of\_log, char *message)}
{	Print message to user's screen in libftp's standard format,
        name\_of\_log must be your program name (if this function called 
from standard handlers then this string is title from FTP structure) and 
message with diagnostic string from anywhere.} 


\subsection{Data transfer procedures}

\ttt{STATUS  \toindex{FtpRetr}(FTP~*, char~*command, char~*inp, char~*out)}
{
	This is basically and single procedure in the library for transfer
	file from the server. One check many option with customizing its style
	of working. This options basically is members of FTP structure such
	as timeout, all handlers, mode, seek. If in continue of working this
	function happen timeout or network broked then this function 
	automatically called I/O or error handler, if one is preseted.
        If handler is not set then FtpRetr return
	status QUIT or LQUIT as signal of type of error (LQUIT is specify
	error happen with local filesystem). \\

}

\ttt{\toindex{FtpGet}(FTP~*, char~*in, char~*out)}
{
 Calls \ts{FtpRetr} with adaptation arguments to transfer file
}

\ttt{\toindex{FtpDirectory}(FTP~*, char~*pat\footnote{This is the first argument for \ts{``ls''} command}, char~*out)}
{
 Transfers files listing from the server, described by \ts{pat}, to the local file \ts{out}.
}

\ttt{\toindex{FtpDir}(FTP~*, char~*out)}
{
 Transfers files listing of the current directory from the server to the local file \ts{out}.
}

\ttt{\toindex{FtpStor}(FTP~*, char~*command, char~*inp, char*~out)}
{
  Store file to the server. Works like FtpRetr.
}

\ttt{\toindex{FtpPut}(FTP~*, char~*in, char~*out)}
{
 Calls \ts{FtpStor} adaptation arguments to transfer file 
}

\ttt{\toindex{FtpCopy}(FTP~*ftp\_from, FTP~*ftp\_to, char~*in, char~*out)}
{
 Transfer file between two server without connection to client's host
}

\ttt{\toindex{FtpLink}(FTP~*ftp1, FTP~*ftp2)}
{
 Make data chanel between both servers. 
}

\ttt{\toindex{FtpPassiveTransfer}(FTP~*ftp\_from, FTP~*ftp\_to, char~*in, char~*out)}
{
 Transfer file between two server via client's cache.
}

\subsection{Server's files read/write procedures}

This library contains special functions for remote files reading and 
writing, without copy in advance them to local files. The functions, 
which are described below, do it. After the data channel 
to a remote file is created, it becomes possible to read and write
 characters using standard Input/Output functions
or using special functions \ts{FtpRead}/\ts{FtpWrite} and/or
\ts{FtpGetc}/\ts{FtpPutc}, which reorganize stream for standard text file, 
under condition that the \ts{ASCII} mode is set. 
\footnote{Of course, such functions as \ts{seek}, \ts{ioctl}, .... 
can not be used.}

\ttt{\toindex{FtpData}(FTP~*, char~*command, char~*param, char~*mode)}
{ Makes data transfer channel, with presending command composed from \ts{command} and \ts{param}. 
The mode must be \ts{``r''} or \ts{``w''}}

\ttt{\toindex{FtpOpenRead}(FTP~*,char~*filename)}
{ Opens file named \ts{filename} for reading on server}

\ttt{\toindex{FtpOpenWrite}(FTP~*,char~*filename)}
{ Creates and opens file named \ts{filename} for writing on server}

\ttt{\toindex{FtpOpenAppend}(FTP~*,char~*filename)}
{ Creates and opens file named \ts{filename} for appending on server}

\ttt{\toindex{FtpOpenDir}(FTP~*, char~*files)}
{
  Creates channel for directory list reading, described by argument \ts{files}.
}

\ttt{STATUS \toindex{FtpRead}(FTP~*)}{
Reads character from data stream. If  \ts{ASCII} mode is set\footnote{By default} converts new line markers.
When the end of file is detected or channel is broken, returns \toindex{EOF}}

\ttt{\toindex{FtpWrite}(FTP~*, char~c)}{
Writes single character to stream, if \ts{ASCII} mode is set converts new line markers.
When channel is broken, returns \toindex{EOF}}

\ttt{int \toindex{FtpGetc}(FTP~*,FILE~*fp)}{
Reads character from data stream specified by fp. Using macros FTPDATA and FTPCMD you can specify stream need for reading. \footnote{Functions FtpGetc and FtpPutc ignores data stream mode, works as binary always}
}

\ttt{STATUS \toindex{FtpPutc}(FTP~*,FILE~*fp, char c)}{
Writes character to data stream specified by fp. Using macros FTPDATA and 
FTPCMD you can specify stream need for reading. \footnote{Functions 
FtpGetc and FtpPutc ignores data stream mode, works as binary always}
}


\ttt{\toindex{FtpClose}(FTP~*)}
{Closes opened channel to server}

\subsection{Other commands for server}

\ttt{\toindex{FtpCommand}(FTP~*, char~*command, char~*param, int~ok1, ok2, ok3, ..., okN, EOF)}
{ Sends a command, composed from \ts{command} and \ts{param} using \ts{sprintf} function. 
Reads an answer from the server. 
When return code from the server is not included to \ts{ok-list}(\ts{ok1},\ts{ok2}...) the sign of code 
will be  inverted.}


\ttt{\toindex{FtpType}(FTP~*,char~mode)}
{Sets transfer mode, such as \ts{'A'},\ts{'I'},\ts{'S'},etc...}

\ttt{\toindex{FtpBinary}(FTP~*)}
{Sets binary mode}

\ttt{\toindex{FtpAscii}(FTP~*)}
{Sets \ts{ASCII} mode}


\ttt{\toindex{FtpMkdir}(FTP~*,char *dirname)}
{Makes  directory on server}

\ttt{\toindex{FtpChdir}(FTP~*,char *dirname)}
{Changes working directory on server}

\ttt{\toindex{FtpChmod}(FTP~*,char *file, int mode)}
{Changes protection of file at remote server, argument mode is protection like inside regular stat's structure.}

\ttt{\toindex{FtpRm}(FTP~*,char *filename)}
{Removes file on server}

\ttt{char~*\toindex{FtpPwd}(FTP~*)}
{Returns the name of working directory on server}

\ttt{int \toindex{FtpSize}(FTP~*,char *filename)}
{Returned size (in bytes) of description's file.}

\ttt{STATUS \toindex{FtpStat}(FTP~*,char~*paten,FTP\_STAT **stat\_list)} 
{Get information about files specified by ``paten''. This function is parser
of text output of command ls or dir on UNIX's, VMS's and MSDOS's operating systems.
The basic problem of realization last one is very different syntaxes of output of
different realization of UNIX's, ftp's daemons and programs such as ``ls'' for making
output list. So.... This function give your garanty of right work only if you use
Berkeley's ftpd or wuftpd on UNIX, PC TCP/IP on MSDOS and Multinet on AXP and VAX VMS's
systems, otherwise function returns empty list}

\ttt{STATUS \toindex{FtpStatFree}(FTP\_STAT *)}
{Free memory from data structures}

\ttt{char *\toindex{FtpSyst}(FTP~*)}{FtpStat return name of operating system on remote server}


\ttt{\toindex{FtpMove}(FTP~*,char *oldfilename, char *newfilename)}
{Renames file from \ts{oldfilename} to \ts{newfilename}}

\ttt{\toindex{FtpPort}(FTP~*, int~a, int~b, int~c, int~d, int~e, int~f)
\footnote{Recommended in non-trivial situations}
}
{ A command for the server for making a new data channel. \ts{a.b.c.d} is an IP address of a client(i.e. your IP address), 
\ts{e*256+f} is a port number}


\ttt{struct hostent *\toindex{FtpGetHost}(char *hostname)
\footnote{Extension of standard function ``gethostbyname''}
}
{Returned pointer to structure \ts{hostent} creating using string 
\ts{hostname}, which contains name of the computer or its IP 
address~\footnote{For example''dxunk8.oea.ihep.su'' or ``**************''} 
}  


\subsection{Functions for sending/receiving control messages to/from server}

\ttt{\toindex{FtpSendMessage}(FTP~*, char~*message)}
{Sends a message to the server}

\ttt{int \toindex{FtpGetMessage}(FTP~*)}
{Receives a message from the server.}

\ttt{\toindex{FtpMessage}(int Number)}
{Gets a message by code.}

\ttt{\toindex{FtpNumber}(char *Message)}
{Extract message's number from string.}

\subsection{High-level functions}

\ttt{FILE *\toindex{FtpFullOpen}(char *filename,char *mode)}
{  
Parses string \ts{filename}, which must contain a string in format or \\
\ts{host/user/password:filename} or \ts{filename},
what corresponds to remote or local file. The second argument is the type of opening, divided into two characters: 
first --- the mode of opening \ts{``r''}, \ts{``w''} or \ts{``a''}, second is the transfer type , if contains character \ts{``b''},
 then the mode is binary.
}

\ttt{STATUS \toindex{FtpFullClose}(FILE *)}
{  
Close file opened by FtpFullOpen.
}

\ttt{STATUS \toindex{FtpFullSyntax}(FtpString source,FtpString host,FtpString user,FtpString password,FtpString file)}
{Make out string ``source'' for next four parameters.}

\ttt{FILE *\toindex{Ftpfopen}(char *file, char *mode)}
{
	Open file specified in libftp's file specification. Works like 
	\ts{fopen}. \look{FTP}
}

\ttt{STATUS \toindex{Ftpfclose}(FILE *fp)}
{
	Close file which opened using Ftpfopen. Works like fclose.
}

\ttt{STATUS \toindex{FtpArchie}
\footnote{FtpArchie for making list and specification of searched files runs 
``archie'' which must be accessible for execution in one of directories listed in PATH environment variable}
(char *what, ARCHIE *result, int number)}{
Find \ts{number} entrys in Archie's database enrolls described by \ts{what}
argument. \ts{result} must be pointer to array of Archie's structures number
of which must be equivalence or higher than \ts{number}. This call return
number of entrys which found in database. If FtpArchie return value lower 
than zero then pointed target not found or archie isn't works}

\ttt{STATUS \toindex{FtpHttpGet}(char *server, int port, char *spec, char *out)}
\label{FtpHttpGet}
{
 This function transfers file from remote computer thru HTTP-server, which must
be described by two firsts arguments server and port. Object which must be took
(probably ftp's file) must be described as HTTP objects. This function always returns
one of several answers such as:

\begin{itemize}
\item[OK] Object was transfered correctly.
\item[QUIT] Socket's communication problem, need to repeat request again.
\item[LQUIT] Problems with opening local file, 
             probably no disk space or permitions problems, need to check regular errno
		variable.
\item[ENOENT] Specified object doesn't exists.
\end{itemize}
}


\section{Simple example of using LIBFTP}

Next example demonstrate very simple using library calls only Ftpfopen 
and Ftpfclose functions which discriminate libftp's file specification:

\input example

For tests works this program you can try run one as:

\bigskip

\% example username/password@hostname:filename myfile.out

\% example myfile.input username/password@hostname:filename.out


\newpage
\input libftp.ind
\newpage
\tableofcontents
\end{document}












