/*
		      Library for ftpd clients.(libftp)
			Copyright by <PERSON><PERSON>
			 All rights reserved.
			
This  library is desined  for  free,  non-commercial  software  creation. 
It is changeable and can be improved. The author would greatly appreciate 
any  advises, new  components  and  patches  of  the  existing  programs.
Commercial  usage is  also  possible  with  participation of it's author.



*/

#include "FtpLibrary.h"

FTP FtpInit = {
  NULL,     /*sock*/
  NULL,  /*data*/
  'A',   /*mode*/
  0,     /*errno*/
  0,     /*ch*/
  NULL,NULL,NULL, NULL, /*funcs*/
  0, /*seek*/
  FTP_HANDLERS, /*flags*/
  {kDefaultFTPTimeoutSeconds,0}, /*timeout 2 min*/
  21, /*Port*/
  0, /*Counter*/
};



FTP *FtpCreateObject()
{
  FTP *new = (FTP *) malloc (sizeof(FTP));

  bcopy(&FtpInit,new,sizeof(FTP));

  return new;
}


