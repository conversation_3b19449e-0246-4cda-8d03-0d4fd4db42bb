<?xml version="1.0" encoding = "Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="7.00"
	Name="visual_c++_project"
	ProjectGUID="{A6AE4B10-E96A-437D-A5A2-E4868EC1C55C}"
	Keyword="Win32Proj">
	<Platforms>
		<Platform
			Name="Win32"/>
	</Platforms>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="Debug"
			IntermediateDirectory="Debug"
			ConfigurationType="1"
			CharacterSet="2">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				PreprocessorDefinitions="WIN32;_DEBUG;_CONSOLE"
				MinimalRebuild="TRUE"
				BasicRuntimeChecks="3"
				RuntimeLibrary="5"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="TRUE"
				DebugInformationFormat="4"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				OutputFile="$(OutDir)/visual_c++_project.exe"
				LinkIncremental="2"
				GenerateDebugInformation="TRUE"
				ProgramDatabaseFile="$(OutDir)/visual_c++_project.pdb"
				SubSystem="1"
				TargetMachine="1"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="Release"
			IntermediateDirectory="Release"
			ConfigurationType="1"
			CharacterSet="2">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="1"
				OmitFramePointers="TRUE"
				PreprocessorDefinitions="WIN32;NDEBUG;_CONSOLE"
				StringPooling="TRUE"
				ExceptionHandling="FALSE"
				RuntimeLibrary="4"
				EnableFunctionLevelLinking="TRUE"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="TRUE"
				DebugInformationFormat="0"
				CompileAs="1"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				OutputFile="$(OutDir)\..\exiso.exe"
				LinkIncremental="1"
				GenerateDebugInformation="TRUE"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				TargetMachine="1"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
		</Configuration>
	</Configurations>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;def;odl;idl;hpj;bat;asm">
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpAbort.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpArchie.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpBye.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpChmod.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpClose.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpCommand.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpConnect.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpCopy.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpData.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpDebug.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpFilenameChecker.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpFull.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpGetHost.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpGood.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpHTTP.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpIO.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpInit.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpLibrary.h">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpLogin.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpMessage.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpMove.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpOpenDir.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpPasv.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpPort.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpPwd.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpRetr.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpSize.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpStat.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpStor.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpSyst.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\FtpType.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\Ftp_etclib.c">
			</File>
			<File
				RelativePath="..\libftp-5.0.1.modified.by.in\Ftpfopen.c">
			</File>
			<File
				RelativePath="..\..\..\..\..\Program Files\Microsoft Visual Studio .NET\Vc7\PlatformSDK\lib\WS2_32.Lib">
			</File>
			<File
				RelativePath="..\extract-xiso.c">
			</File>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
